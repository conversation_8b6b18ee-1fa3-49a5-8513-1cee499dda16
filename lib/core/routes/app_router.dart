import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/screens/screen.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case RoutePath.splashScreen:
        return TransitionUtils.buildTransition(
          const SplashScreen(),
          settings,
        );

      case RoutePath.onboardingScreen:
        return TransitionUtils.buildTransition(
          const OnboardingScreen(),
          settings,
        );

      case RoutePath.bottomNavScreen:
        final arg = args as DashArg?;
        return TransitionUtils.buildTransition(
          BottomNavScreen(args: arg),
          settings,
        );

      // Auth
      case RoutePath.welcomeScreen:
        return TransitionUtils.buildTransition(
          const WelcomeBackScreen(),
          settings,
        );

      case RoutePath.registerScreen:
        return TransitionUtils.buildTransition(
          const RegisterScreen(),
          settings,
        );

      case RoutePath.loginScreen:
        return TransitionUtils.buildTransition(
          const LoginScreen(),
          settings,
        );

      case RoutePath.otpScreen:
        if (args is OtpArg) {
          return TransitionUtils.buildTransition(
            OtpScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Shop
      case RoutePath.shopScreen:
        return TransitionUtils.buildTransition(
          const ShopScreen(),
          settings,
        );

      case RoutePath.shopProductScreen:
        if (args is String) {
          return TransitionUtils.buildTransition(
            ShopProductScreen(category: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.productDetailsScreen:
        if (args is Variation) {
          return TransitionUtils.buildTransition(
            ProductDetailsScreen(product: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.searchProductScreen:
        return TransitionUtils.buildTransition(
          const SearchProductScreen(),
          settings,
        );

      // Cart
      case RoutePath.cartScreen:
        return TransitionUtils.buildTransition(
          const CartScreen(),
          settings,
        );

      case RoutePath.checkoutScreen:
        if (args is CartModel) {
          return TransitionUtils.buildTransition(
            CheckoutScreen(cartModel: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Orders
      case RoutePath.orderScreen:
        return TransitionUtils.buildTransition(
          const OrderScreen(),
          settings,
        );

      case RoutePath.orderDetailsScreen:
        if (args is OrderDetailsArg) {
          return TransitionUtils.buildTransition(
            OrderDetailsScreen(orderDetailsArg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.orderSuccessScreen:
        return TransitionUtils.buildTransition(
          const OrderSuccessScreen(),
          settings,
        );

      // Address
      case RoutePath.addressScreen:
        return TransitionUtils.buildTransition(
          const AddressScreen(),
          settings,
        );

      case RoutePath.addAddressScreen:
        return TransitionUtils.buildTransition(
          const AddAddressScreen(),
          settings,
        );

      case RoutePath.allAddressScreen:
        final arg = args != null ? args as bool : false;
        return TransitionUtils.buildTransition(
          AllAddressScreen(isHomeAddressChange: arg),
          settings,
        );

      // Profile
      case RoutePath.profileScreen:
        return TransitionUtils.buildTransition(
          const ProfileScreen(),
          settings,
        );

      case RoutePath.editProfileScreen:
        return TransitionUtils.buildTransition(
          const EditProfileScreen(),
          settings,
        );

      case RoutePath.deleteAccountScreen:
        return TransitionUtils.buildTransition(
          const DeleteAccountScreen(),
          settings,
        );

      case RoutePath.wishlistScreen:
        return TransitionUtils.buildTransition(
          const WishlistScreen(),
          settings,
        );

      case RoutePath.referralScreen:
        return TransitionUtils.buildTransition(
          const ReferralScreen(),
          settings,
        );

      case RoutePath.notificationScreen:
        return TransitionUtils.buildTransition(
          const NotificationScreen(),
          settings,
        );

      case RoutePath.walletScreen:
        return TransitionUtils.buildTransition(
          const WalletScreen(),
          settings,
        );

      case RoutePath.supportScreen:
        return TransitionUtils.buildTransition(
          const SupportScreen(),
          settings,
        );

      // Group order
      case RoutePath.groupOrderScreen:
        return TransitionUtils.buildTransition(
          const GroupOrderScreen(),
          settings,
        );

      case RoutePath.createGroupOrderScreen:
        return TransitionUtils.buildTransition(
          const CreateGroupOrderScreen(),
          settings,
        );

      case RoutePath.groupOrderShopScreen:
        return TransitionUtils.buildTransition(
          const GroupOrderShopScreen(),
          settings,
        );

      // Webview
      case RoutePath.customWebViewScreen:
        if (args is WebViewArg) {
          return TransitionUtils.buildTransition(
            CustomWebviewScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      default:
        return errorScreen(settings);
    }

    // switch (settings.name) {
    //   case RoutePath.splashScreen:
    //     return buildRoute(const SplashScreen());
    //   case RoutePath.onboardingScreen:
    //     return buildRoute(const OnboardingScreen());
    //   case RoutePath.bottomNavScreen:
    //     final arg = args as DashArg?;
    //     return buildRoute(BottomNavScreen(args: arg));

    //   //  Auth
    //   case RoutePath.welcomeScreen:
    //     return buildRoute(const WelcomeBackScreen());
    //   case RoutePath.registerScreen:
    //     return buildRoute(const RegisterScreen());
    //   case RoutePath.loginScreen:
    //     return buildRoute(const LoginScreen());
    //   case RoutePath.otpScreen:
    //     return buildRoute(const OtpScreen());

    //   // Shop
    //   case RoutePath.shopScreen:
    //     return buildRoute(const ShopScreen());
    //   case RoutePath.shopProductScreen:
    //     if (args is String) {
    //       return buildRoute(ShopProductScreen(category: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   case RoutePath.productDetailsScreen:
    //     if (args is Variation) {
    //       return buildRoute(ProductDetailsScreen(product: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   case RoutePath.searchProductScreen:
    //     return buildRoute(const SearchProductScreen());

    //   // Cart
    //   case RoutePath.cartScreen:
    //     return buildRoute(const CartScreen());
    //   case RoutePath.checkoutScreen:
    //     if (args is CartModel) {
    //       return buildRoute(CheckoutScreen(cartModel: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   // Orders
    //   case RoutePath.orderScreen:
    //     return buildRoute(const OrderScreen());
    //   case RoutePath.orderDetailsScreen:
    //     if (args is OrderModel) {
    //       return buildRoute(OrderDetailsScreen(order: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');
    //   case RoutePath.orderSuccessScreen:
    //     return buildRoute(const OrderSuccessScreen());

    //   case RoutePath.addressScreen:
    //     return buildRoute(const AddressScreen());
    //   case RoutePath.addAddressScreen:
    //     return buildRoute(const AddAddressScreen());

    //   // Profile
    //   case RoutePath.profileScreen:
    //     return buildRoute(const ProfileScreen());
    //   case RoutePath.editProfileScreen:
    //     return buildRoute(const EditProfileScreen());
    //   case RoutePath.deleteAccountScreen:
    //     return buildRoute(const DeleteAccountScreen());
    //   case RoutePath.wishlistScreen:
    //     return buildRoute(const WishlistScreen());

    //   case RoutePath.referralScreen:
    //     return buildRoute(const ReferralScreen());
    //   case RoutePath.notificationScreen:
    //     return buildRoute(const NotificationScreen());
    //   case RoutePath.walletScreen:
    //     return buildRoute(const WalletScreen());

    //   // Group order
    //   case RoutePath.groupOrderScreen:
    //     return buildRoute(const GroupOrderScreen());
    //   case RoutePath.createGroupOrderScreen:
    //     return buildRoute(const CreateGroupOrderScreen());

    //   case RoutePath.customWebViewScreen:
    //     if (args is WebViewArg) {
    //       return buildRoute(CustomWebviewScreen(arg: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   default:
    //     return errorScreen('No route defined for ${settings.name}');
    // }
  }

  static errorScreen(RouteSettings settings) {
    return TransitionUtils.buildTransition(
      ScreenNotFound(routeName: settings.name),
      settings,
    );
  }
}
