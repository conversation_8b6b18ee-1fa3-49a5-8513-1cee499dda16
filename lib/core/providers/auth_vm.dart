import 'dart:async';

import 'package:bottle_king_mobile/core/core.dart';

class AuthVm extends BaseVm {
  AuthUserModel? _user;
  AuthUserModel? get user => _user;

  String get fullNames => "${_user?.firstname} ${_user?.lastname}";

  // Token refresh state management
  bool _isRefreshing = false;
  bool get isRefreshing => _isRefreshing;

  // Queue for pending requests during token refresh
  final List<Completer<String?>> _refreshCompleters = [];

  /// Refreshes the access token using the stored refresh token
  /// Returns the new access token if successful, null otherwise
  Future<String?> refreshAccessToken() async {
    _isRefreshing = true;
    reBuildUI();

    try {
      final newToken = await TokenRefreshService.refreshAccessToken();

      if (newToken == null) {
        // Refresh failed, handle logout
        await _handleRefreshFailure();
      }

      return newToken;
    } finally {
      _isRefreshing = false;
      reBuildUI();
    }
  }

  /// Handles refresh token failure by logging out the user
  Future<void> _handleRefreshFailure() async {
    printty("Handling refresh failure - logging out user",
        logName: "Token Refresh");

    // Complete all pending requests with null
    for (final completer in _refreshCompleters) {
      if (!completer.isCompleted) {
        completer.complete(null);
      }
    }
    _refreshCompleters.clear();

    // Clear stored tokens and user data
    await StorageService.logout();
    _user = null;

    // Navigate to welcome screen
    if (NavKey.appNavKey.currentContext != null) {
      Navigator.pushNamedAndRemoveUntil(
        NavKey.appNavKey.currentContext!,
        RoutePath.welcomeScreen,
        (r) => false,
      );
    }
  }

  /// Checks if the user is authenticated (has valid tokens)
  Future<bool> isAuthenticated() async {
    final accessToken = await StorageService.getAccessToken();
    final refreshToken = await StorageService.getRefreshToken();
    return accessToken != null && refreshToken != null;
  }

  Future<bool> loadUserFromStorage() async {
    final user = await StorageService.getUser();
    if (user != null) {
      _user = user;
      reBuildUI();
      return true;
    }
    return false;
  }

  Future<ApiResponse> createAccount({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String dob,
    String? password,
  }) async {
    final body = {
      "authType": "regular",
      "email": email,
      "phone": phone,
      "firstname": firstName,
      "lastname": lastName,
      "password": password,
      "dob": dob,
      "admin": false, //or false,
      "platform": "mobile",
    };
    printty("createAccount body: $body");
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/register",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        if (password != null) {
          _user = authUserModelFromJson(json.encode(data["data"]["user"]));
          StorageService.storeAccessToken(data["data"]?["accessToken"]);
          StorageService.storeRefreshToken(data["data"]?["refreshToken"]);
          StorageService.storeUser(_user ?? AuthUserModel());
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> login({
    required AuthArg args,
  }) async {
    final body = args.toMap();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/login",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        if (args.password != null) {
          _user = authUserModelFromJson(json.encode(data["data"]["user"]));
          StorageService.storeAccessToken(data["data"]?["accessToken"]);
          StorageService.storeRefreshToken(data["data"]?["refreshToken"]);
          StorageService.storeUser(_user ?? AuthUserModel());
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyOTP(String token) async {
    return await performApiCall(
      url: "/auth/verify",
      method: apiService.post,
      body: {"token": token},
      onSuccess: (data) {
        _user = authUserModelFromJson(json.encode(data["data"]["user"]));
        StorageService.storeAccessToken(data["data"]?["accessToken"]);
        StorageService.storeRefreshToken(data["data"]?["refreshToken"]);
        StorageService.storeUser(_user ?? AuthUserModel());
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> resendOTP() async {
    return await performApiCall(
      url: "/auth/resend-otp",
      method: apiService.post,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  // Future<ApiResponse> getUser() async {
  //   return await performApiCall(
  //     url: "/customer",
  //     method: apiService.getWithAuth,
  //     onSuccess: (data) {
  //       // String token = data["data"]["token"];
  //       // _user = authUserFromJson(json.encode(data["data"]["user"]));
  //       // StorageService.storeAccessToken(token);
  //       // StorageService.storeUser(_user ?? AuthUser());
  //       return apiResponse;
  //     },
  //   );
  // }

  Future<ApiResponse> logout() async {
    try {
      setBusy(true);

      // Clear refresh state
      _isRefreshing = false;
      TokenRefreshService.clearRefreshState();
      for (final completer in _refreshCompleters) {
        if (!completer.isCompleted) {
          completer.complete(null);
        }
      }
      _refreshCompleters.clear();

      // Call logout endpoint
      String url = "/auth/logout";
      apiResponse = await apiService.postWithAuth(body: null, url: url);

      // Clear local storage and user data
      await StorageService.logout();
      _user = null;

      await Future.delayed(const Duration(seconds: 1));

      Navigator.pushNamedAndRemoveUntil(
        NavKey.appNavKey.currentContext!,
        RoutePath.welcomeScreen,
        (r) => false,
      );

      setBusy(false);
      return apiResponse;
    } catch (e) {
      printty(e.toString(), logName: "Logout Error");
      setBusy(false);
      return ApiResponse(success: false, message: e.toString());
    }
  }
}

final authVm = ChangeNotifierProvider((ref) {
  return AuthVm();
});
