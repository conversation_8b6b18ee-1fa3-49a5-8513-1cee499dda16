import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class CartScreen extends ConsumerStatefulWidget {
  const CartScreen({super.key});

  @override
  ConsumerState<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartVm.notifier).getCart();
      ref.read(productVm).getProducts(args: {
        "isMobile": "true",
        "extra_category": "bestsellers",
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    final cartRef = ref.watch(cartVm);
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Cart",
        showBAckBtn: false,
      ),
      body: LoadableContentBuilder(
        isBusy: cartRef.busy(getCartState),
        items: cartRef.regularCartProduct?.items ?? [],
        loadingBuilder: (context) {
          return const SizerLoader(height: 700);
        },
        emptyBuilder: (context) {
          return EmptyState(
            text: "No items in  your cart",
            btnText: "Start shopping",
            onTap: () {
              Navigator.pushNamed(
                context,
                RoutePath.bottomNavScreen,
                arguments: DashArg(index: 1),
              );
            },
          );
        },
        contentBuilder: (context) {
          return RefreshIndicator(
            onRefresh: () async {
              ref.read(cartVm.notifier).getCart();
            },
            child: ListView(
              padding: EdgeInsets.only(
                top: Sizer.width(16),
                bottom: Sizer.width(100),
              ),
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                  ),
                  child: Text(
                    "${cartRef.regularCartProduct?.items?.length ?? 0} item${cartRef.regularCartProduct?.items?.length == 1 ? "" : "s"}",
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const YBox(16),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: Sizer.width(16),
                    right: Sizer.width(16),
                  ),
                  itemBuilder: (ctx, i) {
                    final c = cartRef.regularCartProduct?.items?[i];
                    return CartProductCard(
                      allowSlideToDelete: true,
                      showIncrementBtn: true,
                      cartItem: c,
                    );
                  },
                  separatorBuilder: (_, __) => Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: Sizer.height(10),
                    ),
                    child: const Divider(color: AppColors.grayE6),
                  ),
                  itemCount: cartRef.regularCartProduct?.items?.length ?? 0,
                ),
                const YBox(20),
                const Divider(color: AppColors.greyF7, thickness: 2),

                // You might also like
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(10),
                  ),
                  child: Text(
                    "You might also like",
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (productRef.products.isNotEmpty)
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.64,
                    children:
                        List.generate(productRef.products.take(6).length, (i) {
                      final p = productRef.products.take(6).toList()[i];
                      return HomeProductCard(
                        productVariation: p,
                        fromCart: true,
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.productDetailsScreen,
                            arguments: p,
                          );
                        },
                      );
                    }),
                  ),
              ],
            ),
          );
        },
      ),
      bottomSheet: ((cartRef.regularCartProduct?.items ?? []).isEmpty ||
              cartRef.busy(getCartState))
          ? null
          : Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(16),
                vertical: Sizer.height(12),
              ),
              decoration: const BoxDecoration(
                color: AppColors.white,
                border: Border(
                  top: BorderSide(
                    width: 1,
                    color: AppColors.grayE6,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          "Subtotal: ",
                          style: AppTypography.text12.copyWith(),
                        ),
                        const XBox(4),
                        Text(
                          '${AppUtils.nairaSymbol}${AppUtils.formatNumber(decimalPlaces: 0, number: cartRef.regularCartProduct?.total ?? 0)}',
                          style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: CustomBtn.solid(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.checkoutScreen,
                          arguments: cartRef.cartModel,
                        );
                      },
                      online: true,
                      text:
                          "Place order (${cartRef.regularCartProduct?.items?.length ?? 0})",
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
